"""
表头管理器 - 增强版表头重影修复

功能说明:
- 强化表头缓存清理机制
- 防止中英文表头重影问题
- 提供表头状态检测和验证
- 统一管理所有表格组件的表头
"""

import time
import weakref
import threading
from typing import Dict, List, Optional, Set, Any, Callable
from PyQt5.QtWidgets import QTableWidget, QHeaderView, QWidget, QApplication, QTableWidgetItem
from PyQt5.QtCore import QObject, QTimer, pyqtSignal, Qt
from PyQt5.QtGui import QFont

from src.utils.log_config import setup_logger


class HeaderState:
    """表头状态信息"""
    
    def __init__(self, table_id: str):
        self.table_id = table_id
        self.last_update_time = time.time()
        self.header_labels = []
        self.is_updating = False
        self.update_count = 0
        self.has_shadow_issue = False
        

class TableHeaderManager(QObject):
    """
    表头管理器 - 增强版
    
    负责管理所有表格的表头，防止重影问题，提供以下功能：
    1. 强化表头缓存清理
    2. 表头状态检测和验证
    3. 防重复更新机制
    4. 延迟清理策略
    5. 表头重影检测和修复
    """
    
    # 信号定义
    header_cleaned = pyqtSignal(str)  # 表头清理完成信号 (table_id)
    shadow_detected = pyqtSignal(str, list)  # 检测到重影信号 (table_id, duplicate_labels)
    header_updated = pyqtSignal(str, list)  # 表头更新信号 (table_id, new_labels)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)

        # 🔧 [P3-架构简化] 简化状态管理，专注基础功能
        # 🔧 [P1-1] 使用弱引用管理表格对象，避免生命周期问题
        self.registered_tables: Dict[str, weakref.ReferenceType] = {}
        self.table_cleanup_callbacks: Dict[str, Callable] = {}  # 清理回调函数
        self.cleaning_in_progress = False

        # 🔧 [P2-重影修复] 添加缺失的shadow_detection_enabled属性
        self.shadow_detection_enabled = True  # 启用重影检测功能

        # 🔧 [P2-重影修复] 添加缺失的状态管理属性
        self.header_states: Dict[str, HeaderState] = {}  # 表头状态管理
        self.shadow_fixes = 0  # 重影修复计数
        self._force_next_clean = False  # 强制清理标志

        # 简化配置参数，移除复杂的频率控制
        self.cleanup_delay = 100  # 延迟清理时间（毫秒）
        self.max_update_frequency = 0.1  # 🔧 [P0-3] 添加缺失的最大更新频率属性（秒）

        # 基础统计信息
        self.total_cleanups = 0
        self.last_cleanup_time = 0

        # 🔧 [P0-3] 自动清理机制
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self._auto_cleanup_deleted_tables)
        self.cleanup_timer.setSingleShot(False)
        self.cleanup_timer.setInterval(10000)  # 10秒清理一次已删除的表格引用
        self.cleanup_timer.start()

        # 🔧 [P1-2] 线程安全机制
        self._lock = threading.RLock()  # 可重入锁，支持同一线程多次获取
        self._operation_lock = threading.Lock()  # 操作锁，用于关键操作
        self._cleanup_lock = threading.Lock()  # 清理锁，防止清理操作冲突

        # 🔧 [P1-3] 性能优化机制
        self._detection_frequency_control = {
            'last_detection_time': 0,
            'min_detection_interval': 1.0,  # 最小检测间隔（秒）
            'adaptive_interval': 1.0,  # 自适应间隔
            'consecutive_no_issues': 0,  # 连续无问题次数
            'max_adaptive_interval': 10.0  # 最大自适应间隔
        }
        self._redraw_optimization = {
            'last_redraw_time': {},  # 每个表格的最后重绘时间
            'redraw_cooldown': 0.5,  # 重绘冷却时间
            'batch_operations': [],  # 批量操作队列
            'batch_timer': None  # 批量处理定时器
        }

        self.logger.info("🔧 [P1-3] 性能优化表头管理器初始化完成")
    
    def register_table(self, table_id: str, table: QTableWidget) -> bool:
        """
        注册表格组件
        
        Args:
            table_id: 表格唯一标识
            table: 表格组件
            
        Returns:
            bool: 注册是否成功
        """
        with self._lock:  # 🔧 [P1-2] 线程安全保护
            try:
                # 🔧 [P1-1] 弱引用注册逻辑
                if table_id and table:
                    # 🔧 [P1-1] 创建弱引用和清理回调
                    def cleanup_callback(weak_ref):
                        """当表格对象被垃圾回收时的清理回调"""
                        try:
                            if table_id in self.registered_tables:
                                del self.registered_tables[table_id]
                            if table_id in self.table_cleanup_callbacks:
                                del self.table_cleanup_callbacks[table_id]
                            if table_id in self.header_states:
                                del self.header_states[table_id]
                            self.logger.info(f"🔧 [P1-1] 表格 {table_id} 已自动清理（弱引用回调）")
                        except Exception as e:
                            self.logger.error(f"🔧 [P1-1] 弱引用清理回调失败: {e}")

                    # 🔧 [P1-1] 使用弱引用存储表格对象
                    weak_ref = weakref.ref(table, cleanup_callback)
                    self.registered_tables[table_id] = weak_ref
                    self.table_cleanup_callbacks[table_id] = cleanup_callback

                    self.logger.debug(f"🔧 [P1-1] 表格 {table_id} 注册成功（弱引用）")
                    return True
                else:
                    self.logger.warning(f"注册表格失败：无效的table_id或table对象")
                    return False

            except Exception as e:
                self.logger.error(f"注册表格 {table_id} 失败: {e}")
                return False

    def _get_table_from_weak_ref(self, table_id: str) -> Optional[QTableWidget]:
        """
        🔧 [P1-2] 从弱引用安全获取表格对象（线程安全）

        Args:
            table_id: 表格唯一标识

        Returns:
            QTableWidget对象或None（如果已被回收）
        """
        with self._lock:  # 🔧 [P1-2] 线程安全保护
            try:
                if table_id not in self.registered_tables:
                    return None

                weak_ref = self.registered_tables[table_id]
                table = weak_ref()  # 调用弱引用获取对象

                if table is None:
                    # 对象已被垃圾回收，清理引用
                    self.logger.debug(f"🔧 [P1-1] 表格 {table_id} 对象已被回收，清理引用")
                    if table_id in self.registered_tables:
                        del self.registered_tables[table_id]
                    if table_id in self.table_cleanup_callbacks:
                        del self.table_cleanup_callbacks[table_id]
                    if table_id in self.header_states:
                        del self.header_states[table_id]

                return table

            except Exception as e:
                self.logger.error(f"🔧 [P1-1] 获取弱引用表格失败: {e}")
                return None

    def _should_perform_detection(self) -> bool:
        """
        🔧 [P1-3] 智能检测频率控制

        Returns:
            bool: 是否应该执行检测
        """
        current_time = time.time()
        control = self._detection_frequency_control

        # 检查最小间隔
        if current_time - control['last_detection_time'] < control['adaptive_interval']:
            return False

        # 更新检测时间
        control['last_detection_time'] = current_time
        return True

    def _update_detection_frequency(self, found_issues: bool):
        """
        🔧 [P1-3] 根据检测结果调整检测频率

        Args:
            found_issues: 是否发现问题
        """
        control = self._detection_frequency_control

        if found_issues:
            # 发现问题，增加检测频率
            control['consecutive_no_issues'] = 0
            control['adaptive_interval'] = max(
                control['min_detection_interval'],
                control['adaptive_interval'] * 0.8
            )
            self.logger.debug(f"🔧 [P1-3] 发现问题，提高检测频率至 {control['adaptive_interval']:.1f}s")
        else:
            # 无问题，降低检测频率
            control['consecutive_no_issues'] += 1
            if control['consecutive_no_issues'] >= 3:
                control['adaptive_interval'] = min(
                    control['max_adaptive_interval'],
                    control['adaptive_interval'] * 1.2
                )
                self.logger.debug(f"🔧 [P1-3] 连续无问题，降低检测频率至 {control['adaptive_interval']:.1f}s")

    def _should_perform_redraw(self, table_id: str) -> bool:
        """
        🔧 [P1-3] 智能重绘控制

        Args:
            table_id: 表格ID

        Returns:
            bool: 是否应该执行重绘
        """
        current_time = time.time()
        last_redraw = self._redraw_optimization['last_redraw_time'].get(table_id, 0)

        if current_time - last_redraw < self._redraw_optimization['redraw_cooldown']:
            return False

        self._redraw_optimization['last_redraw_time'][table_id] = current_time
        return True

    def unregister_table(self, table_id: str) -> bool:
        """
        注销表格组件
        
        Args:
            table_id: 表格唯一标识
            
        Returns:
            bool: 注销是否成功
        """
        try:
            # 🔧 [P3-架构简化] 简化注销逻辑
            if table_id in self.registered_tables:
                del self.registered_tables[table_id]
                self.logger.debug(f"🔧 [架构简化] 表格 {table_id} 注销成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"注销表格 {table_id} 失败: {e}")
            return False
    
    def clear_table_header_cache_enhanced(self, table_id: Optional[str] = None) -> bool:
        """
        🔧 [P1-2] 增强版表头缓存清理（线程安全 + 防重复版本）

        Args:
            table_id: 指定表格ID，如果为None则清理所有注册的表格

        Returns:
            bool: 清理是否成功
        """
        with self._operation_lock:  # 🔧 [P1-2] 操作级线程安全保护
            current_time = time.time()

            # 【关键修复】优化频率控制：检查强制清理标志
            force_clean = getattr(self, '_force_next_clean', False)

            # 防止过于频繁的清理，但允许强制清理
            # 🔧 [修复标识] 降低频率限制，允许更频繁的清理操作
            if not force_clean and current_time - self.last_cleanup_time < 0.5:
                self.logger.debug(f"清理过于频繁，跳过（距离上次清理 {current_time - self.last_cleanup_time:.1f}s）")
                return False

            if self.cleaning_in_progress:
                self.logger.debug("表头清理正在进行中，跳过重复清理")
                return False

            # 重置强制清理标志
            self._force_next_clean = False
        
        self.cleaning_in_progress = True
        
        try:
            # 更新统计信息
            self.total_cleanups += 1
            self.last_cleanup_time = time.time()
            
            # 🔧 [P1-1] 确定要清理的表格（弱引用版本）
            tables_to_clean = []
            if table_id:
                if table_id in self.registered_tables:
                    tables_to_clean.append(table_id)
            else:
                tables_to_clean = list(self.registered_tables.keys())

            success_count = 0
            for tid in tables_to_clean:
                if self._clear_single_table_header(tid):
                    success_count += 1
            
            self.logger.info(f"表头缓存清理完成: {success_count}/{len(tables_to_clean)} 个表格成功清理")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"增强版表头清理失败: {e}")
            return False
        finally:
            self.cleaning_in_progress = False
    
    def _clear_single_table_header(self, table_id: str) -> bool:
        """
        🔧 [P1-1] 安全清理单个表格的表头（弱引用版本）

        Args:
            table_id: 表格标识

        Returns:
            bool: 清理是否成功
        """
        try:
            # 🔧 [P1-3] 重绘频率控制
            if not self._should_perform_redraw(table_id):
                self.logger.debug(f"🔧 [P1-3] 表格 {table_id} 重绘过于频繁，跳过清理")
                return False

            # 🔧 [P1-1] 从弱引用获取表格对象
            table = self._get_table_from_weak_ref(table_id)
            if table is None:
                self.logger.warning(f"表格 {table_id} 对象已被回收，跳过清理")
                return False

            state = self.header_states.get(table_id)
            if state and state.is_updating:
                self.logger.debug(f"表格 {table_id} 正在更新中，跳过清理")
                return False

            # 1. 检测并记录当前表头状态
            current_labels = self._get_current_header_labels(table)
            shadow_detected = self._detect_header_shadows(current_labels)

            if shadow_detected:
                self.shadow_fixes += 1
                self.logger.warning(f"检测到表格 {table_id} 存在表头重影: {shadow_detected}")
                self.shadow_detected.emit(table_id, shadow_detected)

            # 2. 🔧 [P0-1] 安全清除表头缓存
            try:
                h_header = table.horizontalHeader()
                v_header = table.verticalHeader()

                # 🔧 [重影修复] 简化重绘机制，只使用必要的update()调用
                if h_header:
                    h_header.update()  # 只调用update()，让Qt控制重绘时机

                    # 清除选择状态避免重影
                    if hasattr(h_header, 'clearSelection'):
                        h_header.clearSelection()

                if v_header:
                    v_header.update()  # 只使用update()

            except RuntimeError as e:
                if "wrapped C/C++ object" in str(e):
                    self.logger.warning(f"表格 {table_id} 表头对象已被删除: {e}")
                    self._remove_deleted_table_reference(table)
                    return False
                else:
                    raise e

            # 移除多余的viewport和table重绘调用
            # 让Qt的事件循环自动处理重绘时机

            # 5. 更新状态信息
            if state:
                state.last_update_time = time.time()
                state.update_count += 1
                state.header_labels = current_labels
                state.has_shadow_issue = bool(shadow_detected)

            self.logger.debug(f"表格 {table_id} 表头清理完成")
            self.header_cleaned.emit(table_id)
            return True

        except Exception as e:
            self.logger.error(f"清理表格 {table_id} 表头失败: {e}")
            return False
    
    def _get_current_header_labels(self, table_id: str) -> List[str]:
        """
        🔧 [P1-1] 安全获取当前表头标签（弱引用版本）

        Args:
            table_id: 表格唯一标识

        Returns:
            List[str]: 表头标签列表
        """
        labels = []
        try:
            # 🔧 [P1-1] 从弱引用获取表格对象
            table = self._get_table_from_weak_ref(table_id)
            if table is None:
                self.logger.warning(f"表格 {table_id} 对象已被回收或不存在")
                return []

            # 🔧 [P0-1] 尝试访问基本属性来验证对象有效性
            try:
                column_count = table.columnCount()
            except RuntimeError as e:
                if "wrapped C/C++ object" in str(e):
                    self.logger.warning(f"表格对象已被删除: {e}")
                    # 弱引用机制会自动清理，这里只需要返回空列表
                    return []
                else:
                    raise e

            # 🔧 [P0-1] 安全获取表头标签
            for i in range(column_count):
                try:
                    header_item = table.horizontalHeaderItem(i)
                    if header_item:
                        labels.append(header_item.text())
                    else:
                        labels.append(f"Column_{i}")
                except RuntimeError as e:
                    if "wrapped C/C++ object" in str(e):
                        self.logger.warning(f"表头项对象已被删除: {e}")
                        break
                    else:
                        raise e

        except Exception as e:
            self.logger.error(f"获取表头标签失败: {e}")

        return labels

    def _is_table_valid(self, table: QTableWidget) -> bool:
        """
        🔧 [P0-1] 检查表格对象是否仍然有效

        Args:
            table: 表格组件

        Returns:
            bool: 对象是否有效
        """
        try:
            if not table or not hasattr(table, 'columnCount'):
                return False

            # 尝试访问基本属性来验证对象有效性
            _ = table.columnCount()
            return True

        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                return False
            else:
                # 其他运行时错误，重新抛出
                raise e
        except Exception:
            return False

    def _remove_deleted_table_reference(self, deleted_table: QTableWidget):
        """
        🔧 [P0-1] 移除已删除表格的引用

        Args:
            deleted_table: 已删除的表格对象
        """
        try:
            tables_to_remove = []
            for table_id, table_ref in self.registered_tables.items():
                if table_ref is deleted_table:
                    tables_to_remove.append(table_id)

            for table_id in tables_to_remove:
                del self.registered_tables[table_id]
                # 同时清理状态信息
                if table_id in self.header_states:
                    del self.header_states[table_id]
                self.logger.info(f"🔧 [P0-1] 已移除已删除表格的引用: {table_id}")

        except Exception as e:
            self.logger.error(f"🔧 [P0-1] 移除已删除表格引用失败: {e}")

    def _auto_cleanup_deleted_tables(self):
        """
        🔧 [P1-2] 自动清理已删除的表格引用（线程安全 + 弱引用）
        """
        with self._cleanup_lock:  # 🔧 [P1-2] 清理操作线程安全保护
            try:
                if self.cleaning_in_progress:
                    return

                deleted_tables = []

                # 🔧 [P1-1] 检查所有弱引用的表格
                for table_id in list(self.registered_tables.keys()):
                    table = self._get_table_from_weak_ref(table_id)
                    if table is None:
                        deleted_tables.append(table_id)

                # 清理已删除的表格（弱引用回调可能已经清理了，这里是双重保险）
                for table_id in deleted_tables:
                    self.logger.info(f"🔧 [P1-1] 自动清理已删除的表格: {table_id}")
                    if table_id in self.registered_tables:
                        del self.registered_tables[table_id]
                    if table_id in self.table_cleanup_callbacks:
                        del self.table_cleanup_callbacks[table_id]
                    if table_id in self.header_states:
                        del self.header_states[table_id]

                if deleted_tables:
                    self.logger.info(f"🔧 [P1-1] 自动清理完成，移除了 {len(deleted_tables)} 个已删除的表格引用")

            except Exception as e:
                self.logger.error(f"🔧 [P1-1] 自动清理失败: {e}")

    def _detect_header_shadows(self, labels: List[str]) -> List[str]:
        """
        🔧 [P1-1] 增强版表头重影检测

        Args:
            labels: 表头标签列表

        Returns:
            List[str]: 重复的标签列表
        """
        if not self.shadow_detection_enabled or not labels:
            return []

        seen = set()
        duplicates = set()

        # 1. 基础重复检测
        for label in labels:
            if label in seen:
                duplicates.add(label)
            else:
                seen.add(label)

        # 2. 检测中英文混合的情况
        chinese_english_pairs = self._detect_chinese_english_duplicates(labels)
        duplicates.update(chinese_english_pairs)

        # 3. 🔧 [P1-1] 检测空白和特殊字符重影
        empty_like_duplicates = self._detect_empty_like_duplicates(labels)
        duplicates.update(empty_like_duplicates)

        # 4. 🔧 [P1-1] 检测相似标签重影（如"工号"和"工号 "）
        similar_duplicates = self._detect_similar_duplicates(labels)
        duplicates.update(similar_duplicates)

        # 5. 🔧 [P1-1] 检测位置重复（同一标签出现在多个位置）
        position_duplicates = self._detect_position_duplicates(labels)
        duplicates.update(position_duplicates)

        # 6. 🔧 [P1-1] 检测视觉重影（相同内容但不同对象）
        visual_duplicates = self._detect_visual_duplicates(labels)
        duplicates.update(visual_duplicates)

        # 7. 🔧 [P1-1] 检测编码问题导致的重影
        encoding_duplicates = self._detect_encoding_duplicates(labels)
        duplicates.update(encoding_duplicates)

        return list(duplicates)
    
    def _detect_chinese_english_duplicates(self, labels: List[str]) -> Set[str]:
        """
        检测中英文对应的重复标签
        
        Args:
            labels: 表头标签列表
            
        Returns:
            Set[str]: 中英文重复对应的标签
        """
        duplicates = set()
        
        # 定义中英文对应关系
        chinese_english_map = {
            '工号': ['employee_id', 'emp_id', 'id'],
            '姓名': ['name', 'employee_name', 'emp_name'],
            '身份证号': ['id_card', 'identity_card'],
            '部门': ['department', 'dept'],
            '职位': ['position', 'title', 'job_title'],
            '基本工资': ['basic_salary', 'base_salary'],
            '职务工资': ['position_salary', 'position_salary_2025'],
            '薪级工资': ['grade_salary', 'grade_salary_2025'],
            '2025年薪级工资': ['grade_salary_2025'],
            '2025年职务工资': ['position_salary_2025'],
            '津贴补贴': ['allowance', 'subsidy'],
            '应发合计': ['gross_pay', 'total_gross'],
            '实发合计': ['net_pay', 'total_net'],
            '扣款合计': ['deduction', 'total_deduction'],
            '住房公积金': ['housing_fund', 'provident_fund'],
            '医疗保险': ['medical_insurance', 'health_insurance'],
            '养老保险': ['pension_insurance', 'retirement_insurance'],
            '失业保险': ['unemployment_insurance'],
            '工伤保险': ['work_injury_insurance'],
            '生育保险': ['maternity_insurance'],
        }
        
        labels_set = set(labels)
        
        for chinese, english_list in chinese_english_map.items():
            if chinese in labels_set:
                for english in english_list:
                    if english in labels_set:
                        duplicates.add(chinese)
                        duplicates.add(english)
                        self.logger.debug(f"检测到中英文重复: {chinese} <-> {english}")
        
        return duplicates

    def _detect_empty_like_duplicates(self, labels: List[str]) -> Set[str]:
        """
        🔧 [P1-1] 检测空白和特殊字符重影

        Args:
            labels: 表头标签列表

        Returns:
            Set[str]: 空白类重复标签
        """
        duplicates = set()
        empty_like_labels = []

        for label in labels:
            # 检测空白、None、特殊字符等
            if not label or label.strip() == '' or label in ['None', 'null', 'undefined', '']:
                empty_like_labels.append(label)

        # 如果有多个空白类标签，都标记为重复
        if len(empty_like_labels) > 1:
            duplicates.update(empty_like_labels)
            self.logger.debug(f"检测到空白类重复标签: {empty_like_labels}")

        return duplicates

    def _detect_similar_duplicates(self, labels: List[str]) -> Set[str]:
        """
        🔧 [P1-1] 检测相似标签重影（如"工号"和"工号 "）

        Args:
            labels: 表头标签列表

        Returns:
            Set[str]: 相似重复标签
        """
        duplicates = set()

        # 创建标准化映射
        normalized_map = {}
        for label in labels:
            if label:
                # 标准化：去除首尾空白、转换为小写
                normalized = label.strip().lower()
                if normalized in normalized_map:
                    # 发现相似重复
                    duplicates.add(label)
                    duplicates.add(normalized_map[normalized])
                    self.logger.debug(f"检测到相似重复标签: '{label}' <-> '{normalized_map[normalized]}'")
                else:
                    normalized_map[normalized] = label

        return duplicates

    def _detect_position_duplicates(self, labels: List[str]) -> Set[str]:
        """
        🔧 [P1-1] 检测位置重复（同一标签出现在多个位置）

        Args:
            labels: 表头标签列表

        Returns:
            Set[str]: 位置重复的标签
        """
        duplicates = set()
        position_map = {}

        for i, label in enumerate(labels):
            if label and label.strip():
                clean_label = label.strip()
                if clean_label in position_map:
                    # 发现同一标签在不同位置
                    duplicates.add(clean_label)
                    self.logger.debug(f"🔧 [P1-1] 检测到位置重复: '{clean_label}' 在位置 {position_map[clean_label]} 和 {i}")
                else:
                    position_map[clean_label] = i

        return duplicates

    def _detect_visual_duplicates(self, labels: List[str]) -> Set[str]:
        """
        🔧 [P1-1] 检测视觉重影（相同内容但不同对象）

        Args:
            labels: 表头标签列表

        Returns:
            Set[str]: 视觉重复的标签
        """
        duplicates = set()

        # 检测Unicode标准化问题
        import unicodedata
        normalized_map = {}

        for label in labels:
            if label and label.strip():
                # 标准化Unicode字符
                normalized = unicodedata.normalize('NFKC', label.strip())

                if normalized in normalized_map:
                    original_label = normalized_map[normalized]
                    if original_label != label:  # 不同的原始字符串但标准化后相同
                        duplicates.add(label)
                        duplicates.add(original_label)
                        self.logger.debug(f"🔧 [P1-1] 检测到视觉重复: '{label}' <-> '{original_label}' (标准化: '{normalized}')")
                else:
                    normalized_map[normalized] = label

        return duplicates

    def _detect_encoding_duplicates(self, labels: List[str]) -> Set[str]:
        """
        🔧 [P1-1] 检测编码问题导致的重影

        Args:
            labels: 表头标签列表

        Returns:
            Set[str]: 编码重复的标签
        """
        duplicates = set()

        # 检测不可见字符和控制字符
        import re
        clean_map = {}

        for label in labels:
            if label:
                # 移除所有不可见字符和控制字符
                clean_label = re.sub(r'[\x00-\x1f\x7f-\x9f\u200b-\u200f\u2028-\u202f\u205f-\u206f\ufeff]', '', label)
                clean_label = clean_label.strip()

                if clean_label and clean_label in clean_map:
                    original_label = clean_map[clean_label]
                    if original_label != label:  # 原始字符串不同但清理后相同
                        duplicates.add(label)
                        duplicates.add(original_label)
                        self.logger.debug(f"🔧 [P1-1] 检测到编码重复: '{repr(label)}' <-> '{repr(original_label)}' (清理后: '{clean_label}')")
                elif clean_label:
                    clean_map[clean_label] = label

        return duplicates

    def start_shadow_monitoring(self, interval_ms: int = 2000):
        """
        🔧 [P1-1] 启动表头重影实时监控

        Args:
            interval_ms: 监控间隔（毫秒）
        """
        if hasattr(self, '_shadow_monitor_timer'):
            self._shadow_monitor_timer.stop()

        from PyQt5.QtCore import QTimer
        self._shadow_monitor_timer = QTimer()
        self._shadow_monitor_timer.timeout.connect(self._monitor_shadow_check)
        self._shadow_monitor_timer.start(interval_ms)

        self.logger.info(f"🔧 [P1-1] 表头重影实时监控已启动，间隔: {interval_ms}ms")

    def stop_shadow_monitoring(self):
        """
        🔧 [P1-1] 停止表头重影实时监控
        """
        if hasattr(self, '_shadow_monitor_timer'):
            self._shadow_monitor_timer.stop()
            self.logger.info("🔧 [P1-1] 表头重影实时监控已停止")

    def _monitor_shadow_check(self):
        """
        🔧 [P1-1] 监控检查回调
        """
        try:
            shadow_count = 0
            for table_id, table in self.registered_tables.items():
                labels = self._get_current_header_labels(table)
                duplicates = self._detect_header_shadows(labels)

                if duplicates:
                    shadow_count += 1
                    self.logger.warning(f"🔧 [P1-1] 监控发现表格 {table_id} 重影: {duplicates}")

                    # 自动修复
                    self._enhanced_fix_table_header(table_id, table, duplicates)

            if shadow_count > 0:
                self.logger.info(f"🔧 [P1-1] 监控周期完成，修复了 {shadow_count} 个表格的重影")

        except Exception as e:
            self.logger.error(f"🔧 [P1-1] 监控检查失败: {e}")

    def force_comprehensive_cleanup(self) -> Dict[str, Any]:
        """
        🔧 [P1-1] 强制全面清理所有表格重影

        Returns:
            Dict[str, Any]: 清理报告
        """
        report = {
            'total_tables': len(self.registered_tables),
            'cleaned_tables': [],
            'failed_tables': [],
            'total_shadows_found': 0,
            'total_shadows_fixed': 0
        }

        try:
            self.logger.info(f"🔧 [P1-1] 开始强制全面清理 {len(self.registered_tables)} 个表格")

            # 🔧 [P0-1] 创建表格列表副本，避免在迭代时修改字典
            tables_to_check = list(self.registered_tables.items())

            for table_id, table in tables_to_check:
                try:
                    # 🔧 [P0-1] 检查表格对象是否仍然有效
                    if not self._is_table_valid(table):
                        self.logger.warning(f"表格 {table_id} 对象已无效，跳过强制清理")
                        self._remove_deleted_table_reference(table)
                        continue

                    # 检测重影
                    labels = self._get_current_header_labels(table)
                    duplicates = self._detect_header_shadows(labels)

                    if duplicates:
                        report['total_shadows_found'] += len(duplicates)
                        self.logger.warning(f"🔧 [P1-1] 表格 {table_id} 发现 {len(duplicates)} 个重影: {duplicates}")

                        # 执行强化修复
                        success = self._enhanced_fix_table_header(table_id, table, duplicates)

                        if success:
                            # 验证修复效果
                            new_labels = self._get_current_header_labels(table)
                            new_duplicates = self._detect_header_shadows(new_labels)

                            fixed_count = len(duplicates) - len(new_duplicates)
                            report['total_shadows_fixed'] += fixed_count

                            if not new_duplicates:
                                report['cleaned_tables'].append(table_id)
                                self.logger.info(f"🔧 [P1-1] 表格 {table_id} 重影完全修复")
                            else:
                                report['failed_tables'].append({
                                    'table_id': table_id,
                                    'remaining_shadows': new_duplicates
                                })
                                self.logger.warning(f"🔧 [P1-1] 表格 {table_id} 部分修复，剩余重影: {new_duplicates}")
                        else:
                            report['failed_tables'].append({
                                'table_id': table_id,
                                'reason': '修复过程失败'
                            })
                    else:
                        # 即使没有重影也进行预防性清理
                        self._preventive_header_cleanup(table_id, table)
                        report['cleaned_tables'].append(table_id)

                except Exception as e:
                    report['failed_tables'].append({
                        'table_id': table_id,
                        'reason': f'处理异常: {e}'
                    })
                    self.logger.error(f"🔧 [P1-1] 处理表格 {table_id} 时异常: {e}")

            success_rate = len(report['cleaned_tables']) / report['total_tables'] * 100 if report['total_tables'] > 0 else 0
            self.logger.info(f"🔧 [P1-1] 强制全面清理完成，成功率: {success_rate:.1f}% ({len(report['cleaned_tables'])}/{report['total_tables']})")

        except Exception as e:
            self.logger.error(f"🔧 [P1-1] 强制全面清理失败: {e}")
            report['error'] = str(e)

        return report

    def _reset_header_style(self, header: QHeaderView):
        """
        重置表头样式，清除可能的样式缓存
        
        Args:
            header: 表头组件
        """
        try:
            # 重置字体
            font = header.font()
            header.setFont(QFont())
            header.setFont(font)
            
            # 重置样式表（如果有的话）
            current_style = header.styleSheet()
            if current_style:
                header.setStyleSheet("")
                header.setStyleSheet(current_style)
            
        except Exception as e:
            self.logger.error(f"重置表头样式失败: {e}")
    
    def validate_header_state(self, table_id: str) -> Dict[str, Any]:
        """
        验证表头状态，检测潜在问题
        
        Args:
            table_id: 表格标识
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'table_id': table_id,
            'is_valid': True,
            'issues': [],
            'recommendations': []
        }
        
        try:
            if table_id not in self.registered_tables:
                result['is_valid'] = False
                result['issues'].append('表格未注册')
                return result
            
            table = self.registered_tables[table_id]
            state = self.header_states.get(table_id)
            
            # 检查表头数量
            column_count = table.columnCount()
            if column_count == 0:
                result['issues'].append('表格没有列')
            
            # 检查表头标签
            labels = self._get_current_header_labels(table)
            duplicates = self._detect_header_shadows(labels)
            
            if duplicates:
                result['is_valid'] = False
                result['issues'].append(f'检测到重复表头: {duplicates}')
                result['recommendations'].append('建议执行表头清理')
            
            # 检查更新频率
            if state and state.update_count > 10:
                current_time = time.time()
                if current_time - state.last_update_time < self.max_update_frequency * 5:
                    result['issues'].append('表头更新过于频繁')
                    result['recommendations'].append('建议检查更新逻辑')
            
            self.logger.debug(f"表格 {table_id} 状态验证完成: {result}")
            
        except Exception as e:
            result['is_valid'] = False
            result['issues'].append(f'验证过程出错: {e}')
            self.logger.error(f"验证表格 {table_id} 状态失败: {e}")
        
        return result
    
    def delayed_cleanup(self, table_id: Optional[str] = None, delay_ms: int = None) -> bool:
        """
        延迟执行表头清理
        
        Args:
            table_id: 表格标识，None表示清理所有
            delay_ms: 延迟时间（毫秒），None使用默认值
            
        Returns:
            bool: 延迟清理是否启动成功
        """
        try:
            actual_delay = delay_ms if delay_ms is not None else self.cleanup_delay
            
            QTimer.singleShot(actual_delay, lambda: self.clear_table_header_cache_enhanced(table_id))
            
            self.logger.debug(f"延迟表头清理已启动: table_id={table_id}, delay={actual_delay}ms")
            return True
            
        except Exception as e:
            self.logger.error(f"启动延迟清理失败: {e}")
            return False
    
    def auto_detect_and_fix_shadows(self) -> Dict[str, List[str]]:
        """
        自动检测并修复所有表格的表头重影
        
        Returns:
            Dict[str, List[str]]: 检测到重影的表格及其重复标签
        """
        shadow_tables = {}
        
        try:
            # 🔧 [P0-1] 创建表格列表副本，避免在迭代时修改字典
            tables_to_check = list(self.registered_tables.items())

            for table_id, table in tables_to_check:
                # 🔧 [P0-1] 检查表格对象是否仍然有效
                if not self._is_table_valid(table):
                    self.logger.warning(f"表格 {table_id} 对象已无效，跳过检测")
                    self._remove_deleted_table_reference(table)
                    continue

                labels = self._get_current_header_labels(table)
                duplicates = self._detect_header_shadows(labels)

                if duplicates:
                    shadow_tables[table_id] = duplicates
                    self.logger.warning(f"自动修复表格 {table_id} 的表头重影: {duplicates}")

                    # 立即执行清理
                    self._clear_single_table_header(table_id)
            
            if shadow_tables:
                self.logger.info(f"自动修复完成，处理了 {len(shadow_tables)} 个表格的重影问题")
            else:
                self.logger.debug("未检测到表头重影问题")
                
        except Exception as e:
            self.logger.error(f"自动检测修复失败: {e}")
        
        return shadow_tables

    def enhanced_auto_detect_and_fix_shadows(self) -> Dict[str, Any]:
        """
        🔧 [P1-3] 【第三层防护】增强版自动检测并修复表头重影（性能优化版）

        相比普通版本，增加了以下功能：
        1. 更详细的检测报告
        2. 预防性清理
        3. 修复效果验证
        4. 性能统计
        5. 🔧 [P1-3] 智能频率控制

        Returns:
            Dict[str, Any]: 详细的检测和修复报告
        """
        # 🔧 [P1-3] 智能频率控制
        if not self._should_perform_detection():
            return {
                'skipped': True,
                'reason': '频率控制跳过检测',
                'next_detection_in': self._detection_frequency_control['adaptive_interval']
            }

        start_time = time.time()
        report = {
            'total_tables': len(self.registered_tables),
            'shadow_tables': {},
            'fixed_tables': [],
            'failed_tables': [],
            'performance': {},
            'recommendations': [],
            'detection_frequency': self._detection_frequency_control['adaptive_interval']
        }

        try:
            self.logger.info(f"🔧 [P1-3] 开始智能表头重影检测，共 {len(self.registered_tables)} 个表格")

            # 🔧 [P1-1] 弱引用版本的表格遍历
            for table_id in list(self.registered_tables.keys()):
                table_start_time = time.time()

                try:
                    # 🔧 [P1-1] 从弱引用获取表格对象
                    table = self._get_table_from_weak_ref(table_id)
                    if table is None:
                        self.logger.warning(f"表格 {table_id} 对象已被回收，跳过检测")
                        continue

                    # 1. 获取当前表头状态
                    labels = self._get_current_header_labels(table_id)
                    duplicates = self._detect_header_shadows(labels)

                    if duplicates:
                        report['shadow_tables'][table_id] = {
                            'duplicates': duplicates,
                            'total_headers': len(labels),
                            'duplicate_count': len(duplicates)
                        }

                        self.logger.warning(f"检测到表格 {table_id} 的表头重影: {duplicates}")

                        # 2. 执行修复
                        fix_success = self._enhanced_fix_table_header(table_id, table, duplicates)

                        if fix_success:
                            # 3. 验证修复效果
                            new_labels = self._get_current_header_labels(table_id)
                            new_duplicates = self._detect_header_shadows(new_labels)

                            if not new_duplicates:
                                report['fixed_tables'].append(table_id)
                                self.logger.info(f"表格 {table_id} 表头重影修复成功")
                            else:
                                report['failed_tables'].append({
                                    'table_id': table_id,
                                    'reason': f"修复后仍有重影: {new_duplicates}"
                                })
                                self.logger.error(f"表格 {table_id} 表头重影修复失败，仍有重影: {new_duplicates}")
                        else:
                            report['failed_tables'].append({
                                'table_id': table_id,
                                'reason': "修复过程失败"
                            })
                    else:
                        # 即使没有重影，也进行预防性清理
                        self._preventive_header_cleanup(table_id, table)

                    # 记录单表处理时间
                    table_time = (time.time() - table_start_time) * 1000
                    report['performance'][table_id] = f"{table_time:.2f}ms"

                except Exception as table_error:
                    self.logger.error(f"处理表格 {table_id} 时发生错误: {table_error}")
                    report['failed_tables'].append({
                        'table_id': table_id,
                        'reason': str(table_error)
                    })

            # 生成建议
            self._generate_recommendations(report)

            total_time = (time.time() - start_time) * 1000
            report['performance']['total_time'] = f"{total_time:.2f}ms"

            # 🔧 [P1-3] 根据检测结果更新频率
            found_issues = len(report['shadow_tables']) > 0
            self._update_detection_frequency(found_issues)

            self.logger.info(f"🔧 [P1-3] 智能表头重影检测完成，耗时 {total_time:.2f}ms")
            self.logger.info(f"检测结果：{len(report['shadow_tables'])} 个重影表格，{len(report['fixed_tables'])} 个修复成功，{len(report['failed_tables'])} 个修复失败")
            self.logger.debug(f"🔧 [P1-3] 下次检测间隔：{self._detection_frequency_control['adaptive_interval']:.1f}s")

        except Exception as e:
            self.logger.error(f"增强版表头重影检测失败: {e}")
            report['error'] = str(e)
            # 🔧 [P1-3] 出错时也更新频率（保守策略）
            self._update_detection_frequency(True)

        return report

    def _enhanced_fix_table_header(self, table_id: str, table: QTableWidget, duplicates: List[str]) -> bool:
        """
        🔧 [P1-1] 增强版表头修复方法（保持兼容性）

        Args:
            table_id: 表格ID
            table: 表格组件（可选，会从弱引用获取）
            duplicates: 重复的表头标签

        Returns:
            bool: 修复是否成功
        """
        try:
            # 🔧 [P1-1] 优先从弱引用获取表格对象
            if table is None:
                table = self._get_table_from_weak_ref(table_id)
                if table is None:
                    self.logger.warning(f"表格 {table_id} 对象已被回收，无法修复")
                    return False

            self.logger.debug(f"🔧 [P1-1] 开始增强修复表格 {table_id} 的表头重影: {duplicates}")

            # 1. 保存当前状态
            original_labels = self._get_current_header_labels(table)

            # 2. 🔧 [P1-1] 多轮修复策略
            max_attempts = 3
            for attempt in range(max_attempts):
                self.logger.debug(f"🔧 [P1-1] 表格 {table_id} 修复尝试 {attempt + 1}/{max_attempts}")

                # 执行深度清理
                success = self._deep_header_cleanup(table_id, table)
                if not success:
                    self.logger.warning(f"表格 {table_id} 第 {attempt + 1} 次深度清理失败")
                    continue

                # 等待清理完成
                QApplication.processEvents()
                time.sleep(0.02)  # 20ms

                # 🔧 [P1-1] 验证修复效果
                current_labels = self._get_current_header_labels(table)
                remaining_duplicates = self._detect_header_shadows(current_labels)

                if not remaining_duplicates:
                    self.logger.info(f"🔧 [P1-1] 表格 {table_id} 修复成功，第 {attempt + 1} 次尝试")

                    # 重新设置表头（如果有原始数据）
                    if hasattr(table, 'original_headers') and table.original_headers:
                        self._safe_restore_headers(table, table.original_headers)

                    return True
                else:
                    self.logger.warning(f"🔧 [P1-1] 表格 {table_id} 第 {attempt + 1} 次修复后仍有重影: {remaining_duplicates}")

            # 所有尝试都失败
            self.logger.error(f"🔧 [P1-1] 表格 {table_id} 经过 {max_attempts} 次尝试仍无法完全修复重影")
            return False

        except Exception as e:
            self.logger.error(f"🔧 [P1-1] 增强修复表格 {table_id} 失败: {e}")
            return False

    def _deep_header_cleanup(self, table_id: str, table: QTableWidget) -> bool:
        """
        🔧 [P1-1] 增强版深度表头清理

        Args:
            table_id: 表格ID
            table: 表格组件

        Returns:
            bool: 清理是否成功
        """
        try:
            self.logger.debug(f"🔧 [P1-1] 开始深度清理表格 {table_id}")

            # 1. 基础清理
            basic_success = self._clear_single_table_header(table_id)

            # 2. 🔧 [P1-1] 强化表头清理
            h_header = table.horizontalHeader()
            v_header = table.verticalHeader()

            if h_header:
                # 强制重置表头状态
                h_header.reset()
                h_header.clearSelection()

                # 🔧 [P1-1] 清除表头缓存和样式
                if hasattr(h_header, 'setStyleSheet'):
                    h_header.setStyleSheet("")  # 清除样式表

                # 🔧 [P1-1] 强制刷新表头几何信息
                h_header.updateGeometry()
                h_header.update()

            if v_header:
                # 同样处理垂直表头
                v_header.reset()
                v_header.clearSelection()

                if hasattr(v_header, 'setStyleSheet'):
                    v_header.setStyleSheet("")

                v_header.updateGeometry()
                v_header.update()

            # 3. 🔧 [P1-1] 强化表格内部清理
            table.clearContents()
            table.clearSelection()

            # 清除表格的样式缓存
            if hasattr(table, 'setStyleSheet'):
                current_style = table.styleSheet()
                table.setStyleSheet("")  # 临时清除
                QApplication.processEvents()  # 让Qt处理样式变更
                table.setStyleSheet(current_style)  # 恢复样式

            # 4. 🔧 [P1-1] 强制重新计算表格布局
            table.updateGeometry()
            table.update()

            # 5. 🔧 [P1-1] 等待Qt事件处理完成
            QApplication.processEvents()

            self.logger.debug(f"🔧 [P1-1] 表格 {table_id} 深度清理完成")
            return basic_success

        except Exception as e:
            self.logger.error(f"🔧 [P1-1] 深度清理表格 {table_id} 失败: {e}")
            return False

    def _preventive_header_cleanup(self, table_id: str, table: QTableWidget):
        """
        预防性表头清理（即使没有检测到重影也进行轻度清理）

        Args:
            table_id: 表格ID
            table: 表格组件
        """
        try:
            h_header = table.horizontalHeader()
            if h_header:
                # 🔧 [P1-简化重绘] 移除updateGeometry，只使用update()
                h_header.update()

            viewport = table.viewport()
            if viewport:
                viewport.update()

        except Exception as e:
            self.logger.debug(f"预防性清理表格 {table_id} 失败: {e}")

    def _safe_restore_headers(self, table: QTableWidget, headers: List[str]):
        """
        安全地恢复表头

        Args:
            table: 表格组件
            headers: 表头列表
        """
        try:
            if table.columnCount() != len(headers):
                table.setColumnCount(len(headers))

            for i, header in enumerate(headers):
                if i < table.columnCount():
                    header_item = QTableWidgetItem(str(header))
                    table.setHorizontalHeaderItem(i, header_item)

        except Exception as e:
            self.logger.error(f"安全恢复表头失败: {e}")

    def _generate_recommendations(self, report: Dict[str, Any]):
        """
        根据检测结果生成建议

        Args:
            report: 检测报告
        """
        recommendations = []

        if len(report['shadow_tables']) > 0:
            recommendations.append("检测到表头重影问题，建议在数据设置前进行预清理")

        if len(report['failed_tables']) > 0:
            recommendations.append("部分表格修复失败，建议检查表格组件状态")

        if report['total_tables'] > 10:
            recommendations.append("表格数量较多，建议启用延迟清理机制")

        report['recommendations'] = recommendations

    def _on_table_changed(self, table_id: str):
        """
        表格内容变化回调
        
        Args:
            table_id: 表格标识
        """
        try:
            state = self.header_states.get(table_id)
            if state:
                current_time = time.time()
                
                # 防止过于频繁的更新
                if current_time - state.last_update_time < self.max_update_frequency:
                    return
                
                # 延迟清理表头
                self.delayed_cleanup(table_id, 100)
                
        except Exception as e:
            self.logger.error(f"处理表格 {table_id} 变化失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'registered_tables': len(self.registered_tables),
            'total_cleanups': self.total_cleanups,
            'shadow_fixes': self.shadow_fixes,
            'last_cleanup_time': self.last_cleanup_time,
            'cleaning_in_progress': self.cleaning_in_progress,
            'table_states': {tid: {
                'update_count': state.update_count,
                'has_shadow_issue': state.has_shadow_issue,
                'last_update': state.last_update_time
            } for tid, state in self.header_states.items()}
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_cleanups = 0
        self.shadow_fixes = 0
        self.last_cleanup_time = 0
        
        for state in self.header_states.values():
            state.update_count = 0
            state.has_shadow_issue = False
        
        self.logger.info("表头管理器统计信息已重置")
    
    def force_clear_next_time(self):
        """设置强制清理标志，确保下次清理不被频率限制"""
        self._force_next_clean = True
        self.logger.debug("已设置强制清理标志，下次清理将忽略频率限制")
    
    def clear_table_header_immediately(self, table_id: Optional[str] = None) -> bool:
        """立即执行表头清理，忽略频率限制"""
        self._force_next_clean = True
        return self.clear_table_header_cache_enhanced(table_id)


# 全局表头管理器实例（单例模式）
_global_header_manager = None

def get_global_header_manager() -> TableHeaderManager:
    """
    获取全局表头管理器实例

    Returns:
        TableHeaderManager: 全局管理器实例
    """
    global _global_header_manager
    if _global_header_manager is None:
        _global_header_manager = TableHeaderManager()
    return _global_header_manager

def reset_global_header_manager():
    """
    🔧 [P2-重影修复] 重置全局表头管理器实例

    用于强制重新创建全局实例，确保新的修复生效
    """
    global _global_header_manager
    _global_header_manager = None