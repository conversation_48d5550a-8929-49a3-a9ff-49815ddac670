#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整修复效果的脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_expandable_model_fix():
    """测试ExpandableTableModel修复"""
    print("=== 测试ExpandableTableModel修复 ===")
    
    try:
        from src.gui.prototype.widgets.virtualized_expandable_table import ExpandableTableModel
        
        # 创建模型实例
        model = ExpandableTableModel()
        
        # 测试_get_employee_id_from_row方法是否存在
        if hasattr(model, '_get_employee_id_from_row'):
            print("✓ ExpandableTableModel._get_employee_id_from_row方法已添加")
            
            # 测试方法功能
            test_data = {'工号': '001', 'employee_name': '张三'}
            result = model._get_employee_id_from_row(test_data, 0)
            print(f"  测试结果: {result}")
            
            return True
        else:
            print("✗ ExpandableTableModel._get_employee_id_from_row方法缺失")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_format_renderer_debug_logs():
    """测试FormatRenderer调试日志是否添加"""
    print("\n=== 测试FormatRenderer调试日志 ===")
    
    try:
        # 读取FormatRenderer源码，检查调试日志
        format_renderer_path = project_root / "src" / "modules" / "format_management" / "format_renderer.py"
        with open(format_renderer_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键调试日志是否存在
        debug_patterns = [
            "🔧 [DEBUG] table_type=",
            "🔧 [DEBUG] formatted_df.columns=",
            "🔧 [DEBUG] existing_display_fields为空",
            "🔧 [DEBUG] display_fields为空"
        ]
        
        all_found = True
        for pattern in debug_patterns:
            if pattern in content:
                print(f"✓ 找到调试日志: {pattern}")
            else:
                print(f"✗ 缺少调试日志: {pattern}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_system_startup():
    """测试系统是否能正常启动（不运行GUI）"""
    print("\n=== 测试系统组件初始化 ===")
    
    try:
        # 测试关键组件能否正常初始化
        from src.modules.format_management.field_registry import FieldRegistry
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.format_renderer import FormatRenderer
        
        mapping_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(mapping_path))
        field_registry.load_mappings()
        print("✓ FieldRegistry初始化成功")
        
        format_config = FormatConfig()
        print("✓ FormatConfig初始化成功")
        
        format_renderer = FormatRenderer(format_config, field_registry)
        print("✓ FormatRenderer初始化成功")
        
        # 测试A岗职工display_fields
        display_fields = field_registry.get_display_fields('a_grade_employees')
        print(f"✓ A岗职工显示字段: {len(display_fields)}个")
        
        return True
        
    except Exception as e:
        print(f"系统组件初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("开始测试完整修复效果")
    print("=" * 60)
    
    # 测试1: ExpandableTableModel修复
    test1_passed = test_expandable_model_fix()
    
    # 测试2: FormatRenderer调试日志
    test2_passed = test_format_renderer_debug_logs()
    
    # 测试3: 系统组件初始化
    test3_passed = test_system_startup()
    
    print("\n" + "=" * 60)
    print("修复测试结果汇总:")
    print(f"  ExpandableTableModel修复: {'通过' if test1_passed else '失败'}")
    print(f"  FormatRenderer调试日志: {'通过' if test2_passed else '失败'}")
    print(f"  系统组件初始化: {'通过' if test3_passed else '失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print("\n✓ 所有修复测试通过！")
        print("现在可以启动系统测试实际效果")
    else:
        print("\n✗ 部分修复测试失败，需要进一步检查")