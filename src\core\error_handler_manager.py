#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 [P2-3] 统一错误处理管理器

本模块提供统一的错误处理和用户反馈机制，包括：
- 错误分类和优先级管理
- 用户友好的错误消息
- 错误恢复策略
- 错误统计和分析
"""

import time
import traceback
from enum import Enum
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from PyQt5.QtWidgets import QMessageBox, QApplication
from PyQt5.QtCore import QTimer, pyqtSignal, QObject

from src.utils.log_config import get_module_logger
from src.core.event_bus import EventBus, ErrorEvent


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"           # 轻微错误，不影响主要功能
    MEDIUM = "medium"     # 中等错误，影响部分功能
    HIGH = "high"         # 严重错误，影响主要功能
    CRITICAL = "critical" # 致命错误，系统无法继续运行


class ErrorCategory(Enum):
    """错误分类"""
    DATA_ERROR = "data_error"           # 数据相关错误
    UI_ERROR = "ui_error"               # 界面相关错误
    SYSTEM_ERROR = "system_error"       # 系统相关错误
    NETWORK_ERROR = "network_error"     # 网络相关错误
    FILE_ERROR = "file_error"           # 文件操作错误
    DATABASE_ERROR = "database_error"   # 数据库错误
    VALIDATION_ERROR = "validation_error" # 验证错误


@dataclass
class ErrorInfo:
    """错误信息"""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    user_message: str
    details: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    recovery_actions: List[str] = field(default_factory=list)
    auto_recovery: bool = False


class ErrorHandlerManager(QObject):
    """
    🔧 [P2-3] 统一错误处理管理器
    """
    
    # 错误处理信号
    error_occurred = pyqtSignal(object)  # ErrorInfo
    error_recovered = pyqtSignal(str)    # error_id
    
    def __init__(self):
        super().__init__()
        self.logger = get_module_logger(__name__)
        self.event_bus = EventBus()
        
        # 错误统计
        self.error_stats = {
            'total_errors': 0,
            'by_category': {},
            'by_severity': {},
            'recovery_success': 0,
            'recovery_failed': 0
        }
        
        # 错误历史记录
        self.error_history: List[ErrorInfo] = []
        self.max_history_size = 1000
        
        # 恢复策略映射
        self.recovery_strategies: Dict[str, Callable] = {}
        
        # 用户消息模板
        self.message_templates = {
            ErrorCategory.DATA_ERROR: {
                ErrorSeverity.LOW: "数据处理时遇到小问题，已自动修复",
                ErrorSeverity.MEDIUM: "数据格式存在问题，请检查数据源",
                ErrorSeverity.HIGH: "数据严重错误，无法继续处理",
                ErrorSeverity.CRITICAL: "数据损坏，系统无法继续运行"
            },
            ErrorCategory.UI_ERROR: {
                ErrorSeverity.LOW: "界面显示异常，正在尝试修复",
                ErrorSeverity.MEDIUM: "界面功能受限，请尝试刷新",
                ErrorSeverity.HIGH: "界面严重错误，建议重启程序",
                ErrorSeverity.CRITICAL: "界面崩溃，程序将退出"
            },
            ErrorCategory.SYSTEM_ERROR: {
                ErrorSeverity.LOW: "系统运行异常，已自动处理",
                ErrorSeverity.MEDIUM: "系统功能受影响，正在恢复",
                ErrorSeverity.HIGH: "系统严重错误，功能受限",
                ErrorSeverity.CRITICAL: "系统致命错误，程序将退出"
            }
        }
        
        self.logger.info("🔧 [P2-3] 错误处理管理器初始化完成")

    def handle_error(self, 
                    exception: Exception,
                    category: ErrorCategory,
                    severity: ErrorSeverity,
                    context: Optional[Dict[str, Any]] = None,
                    user_message: Optional[str] = None,
                    auto_recovery: bool = False) -> str:
        """
        🔧 [P2-3] 处理错误
        
        Args:
            exception: 异常对象
            category: 错误分类
            severity: 错误严重程度
            context: 错误上下文
            user_message: 自定义用户消息
            auto_recovery: 是否尝试自动恢复
            
        Returns:
            错误ID
        """
        try:
            # 生成错误ID
            error_id = f"{category.value}_{int(time.time() * 1000)}"
            
            # 获取用户友好消息
            if not user_message:
                user_message = self._get_user_message(category, severity)
            
            # 创建错误信息
            error_info = ErrorInfo(
                error_id=error_id,
                category=category,
                severity=severity,
                message=str(exception),
                user_message=user_message,
                details=context or {},
                stack_trace=traceback.format_exc(),
                auto_recovery=auto_recovery
            )
            
            # 记录错误
            self._record_error(error_info)
            
            # 发布错误事件
            self._publish_error_event(error_info)
            
            # 显示用户反馈
            self._show_user_feedback(error_info)
            
            # 尝试自动恢复
            if auto_recovery:
                self._attempt_auto_recovery(error_info)
            
            # 发出信号
            self.error_occurred.emit(error_info)
            
            return error_id
            
        except Exception as e:
            self.logger.critical(f"🔧 [P2-3] 错误处理器自身发生错误: {e}")
            return "error_handler_failed"

    def _get_user_message(self, category: ErrorCategory, severity: ErrorSeverity) -> str:
        """获取用户友好的错误消息"""
        try:
            return self.message_templates.get(category, {}).get(
                severity, 
                f"发生{severity.value}级别的{category.value}错误"
            )
        except Exception:
            return "系统发生未知错误"

    def _record_error(self, error_info: ErrorInfo):
        """记录错误信息"""
        try:
            # 添加到历史记录
            self.error_history.append(error_info)
            
            # 限制历史记录大小
            if len(self.error_history) > self.max_history_size:
                self.error_history = self.error_history[-self.max_history_size:]
            
            # 更新统计信息
            self.error_stats['total_errors'] += 1
            
            category_key = error_info.category.value
            self.error_stats['by_category'][category_key] = \
                self.error_stats['by_category'].get(category_key, 0) + 1
            
            severity_key = error_info.severity.value
            self.error_stats['by_severity'][severity_key] = \
                self.error_stats['by_severity'].get(severity_key, 0) + 1
            
            # 记录到日志
            log_level = self._get_log_level(error_info.severity)
            self.logger.log(
                log_level,
                f"🔧 [P2-3] 错误记录: {error_info.error_id} - "
                f"{error_info.category.value}/{error_info.severity.value} - "
                f"{error_info.message}"
            )
            
        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 记录错误信息失败: {e}")

    def _get_log_level(self, severity: ErrorSeverity) -> int:
        """根据错误严重程度获取日志级别"""
        import logging
        mapping = {
            ErrorSeverity.LOW: logging.WARNING,
            ErrorSeverity.MEDIUM: logging.ERROR,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }
        return mapping.get(severity, logging.ERROR)

    def _publish_error_event(self, error_info: ErrorInfo):
        """发布错误事件"""
        try:
            error_event = ErrorEvent(
                event_type="error",  # 添加必需的event_type参数
                error_type=error_info.category.value,
                error_message=error_info.message,
                error_details={
                    'error_id': error_info.error_id,
                    'severity': error_info.severity.value,
                    'user_message': error_info.user_message,
                    'context': error_info.details
                },
                stack_trace=error_info.stack_trace
            )

            self.event_bus.publish(error_event)

        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 发布错误事件失败: {e}")

    def _show_user_feedback(self, error_info: ErrorInfo):
        """显示用户反馈"""
        try:
            # 根据严重程度决定是否显示对话框
            if error_info.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                self._show_error_dialog(error_info)
            elif error_info.severity == ErrorSeverity.MEDIUM:
                # 中等错误显示状态栏消息
                self._show_status_message(error_info)
            # 轻微错误只记录日志，不打扰用户
            
        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 显示用户反馈失败: {e}")

    def _show_error_dialog(self, error_info: ErrorInfo):
        """显示错误对话框"""
        try:
            if not QApplication.instance():
                return
            
            # 使用QTimer确保在主线程中显示
            QTimer.singleShot(0, lambda: self._create_error_dialog(error_info))
            
        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 显示错误对话框失败: {e}")

    def _create_error_dialog(self, error_info: ErrorInfo):
        """创建错误对话框"""
        try:
            msg_box = QMessageBox()
            
            # 设置图标
            if error_info.severity == ErrorSeverity.CRITICAL:
                msg_box.setIcon(QMessageBox.Critical)
            else:
                msg_box.setIcon(QMessageBox.Warning)
            
            # 设置标题和消息
            msg_box.setWindowTitle("系统错误")
            msg_box.setText(error_info.user_message)
            
            # 设置详细信息
            details = f"错误ID: {error_info.error_id}\n"
            details += f"分类: {error_info.category.value}\n"
            details += f"严重程度: {error_info.severity.value}\n"
            details += f"详细信息: {error_info.message}\n"
            if error_info.details:
                details += f"上下文: {error_info.details}\n"
            
            msg_box.setDetailedText(details)
            msg_box.setStandardButtons(QMessageBox.Ok)
            
            # 如果有恢复操作，添加重试按钮
            if error_info.recovery_actions:
                msg_box.addButton("重试", QMessageBox.ActionRole)
            
            msg_box.exec_()
            
        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 创建错误对话框失败: {e}")

    def _show_status_message(self, error_info: ErrorInfo):
        """显示状态栏消息"""
        try:
            # 这里可以通过事件总线通知主窗口显示状态消息
            status_event = {
                'type': 'status_message',
                'message': error_info.user_message,
                'timeout': 5000,  # 5秒
                'severity': error_info.severity.value
            }
            
            # 发布状态消息事件
            from src.core.event_bus import Event
            self.event_bus.publish(Event(
                event_type="status_message",
                data=status_event
            ))
            
        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 显示状态消息失败: {e}")

    def _attempt_auto_recovery(self, error_info: ErrorInfo):
        """尝试自动恢复"""
        try:
            recovery_key = f"{error_info.category.value}_{error_info.severity.value}"
            recovery_func = self.recovery_strategies.get(recovery_key)
            
            if recovery_func:
                self.logger.info(f"🔧 [P2-3] 尝试自动恢复: {error_info.error_id}")
                
                if recovery_func(error_info):
                    self.error_stats['recovery_success'] += 1
                    self.error_recovered.emit(error_info.error_id)
                    self.logger.info(f"🔧 [P2-3] 自动恢复成功: {error_info.error_id}")
                else:
                    self.error_stats['recovery_failed'] += 1
                    self.logger.warning(f"🔧 [P2-3] 自动恢复失败: {error_info.error_id}")
            
        except Exception as e:
            self.logger.error(f"🔧 [P2-3] 自动恢复过程失败: {e}")
            self.error_stats['recovery_failed'] += 1

    def register_recovery_strategy(self, 
                                 category: ErrorCategory, 
                                 severity: ErrorSeverity, 
                                 recovery_func: Callable[[ErrorInfo], bool]):
        """
        🔧 [P2-3] 注册恢复策略
        
        Args:
            category: 错误分类
            severity: 错误严重程度
            recovery_func: 恢复函数，返回True表示恢复成功
        """
        key = f"{category.value}_{severity.value}"
        self.recovery_strategies[key] = recovery_func
        self.logger.info(f"🔧 [P2-3] 注册恢复策略: {key}")

    def get_error_statistics(self) -> Dict[str, Any]:
        """
        🔧 [P2-3] 获取错误统计信息
        
        Returns:
            错误统计信息
        """
        return {
            'stats': self.error_stats.copy(),
            'recent_errors': [
                {
                    'error_id': err.error_id,
                    'category': err.category.value,
                    'severity': err.severity.value,
                    'message': err.message,
                    'timestamp': err.timestamp
                }
                for err in self.error_history[-10:]  # 最近10个错误
            ]
        }

    def clear_error_history(self):
        """清空错误历史记录"""
        self.error_history.clear()
        self.error_stats = {
            'total_errors': 0,
            'by_category': {},
            'by_severity': {},
            'recovery_success': 0,
            'recovery_failed': 0
        }
        self.logger.info("🔧 [P2-3] 错误历史记录已清空")


# 全局错误处理管理器实例
_error_handler_manager = None


def get_error_handler() -> ErrorHandlerManager:
    """获取全局错误处理管理器实例"""
    global _error_handler_manager
    if _error_handler_manager is None:
        _error_handler_manager = ErrorHandlerManager()
    return _error_handler_manager


# 便捷函数
def handle_error(exception: Exception,
                category: ErrorCategory,
                severity: ErrorSeverity,
                context: Optional[Dict[str, Any]] = None,
                user_message: Optional[str] = None,
                auto_recovery: bool = False) -> str:
    """便捷的错误处理函数"""
    return get_error_handler().handle_error(
        exception, category, severity, context, user_message, auto_recovery
    )
